#!/usr/bin/env python3
"""
Servidor MCP integrado com Google Gemini usando Gradio.

Este servidor combina o poder do Gemini para interpretação de linguagem natural
com funcionalidades de busca e análise de dados do OSF.io, incluindo capacidades
de visualização e análise de dados científicos.

Uso:
    python gemini_mcp_server.py

O servidor estará disponível em:
    http://localhost:7861
"""

import os
import json
import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from typing import Dict, Tuple, Optional
from datetime import datetime

# Importar dotenv se disponível
try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False
    print("⚠️  python-dotenv não encontrado. Usando variáveis de ambiente do sistema.")

# Importar Google Gemini
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False
    print("⚠️  google-generativeai não encontrado. Funcionalidades do Gemini desabilitadas.")

# Importar módulo OSF scraper
try:
    from src.osf_scraper import search_osf, download_osf_files
    HAS_OSF_SCRAPER = True
except ImportError:
    HAS_OSF_SCRAPER = False
    print("⚠️  Módulo src.osf_scraper não encontrado. Funcionalidades OSF desabilitadas.")

# Definir constantes para compatibilidade
HAS_QUADRATIC_SOLVER = False
HAS_ADVANCED_PLOTTING = False


def extract_gemini_response_text(response) -> str:
    """Extrai texto de uma resposta Gemini, ignorando partes function_call."""
    if hasattr(response, 'candidates') and response.candidates and hasattr(response.candidates[0].content, 'parts'):
        result_text = ""
        for part in response.candidates[0].content.parts:
            if hasattr(part, 'text') and part.text:
                result_text += part.text
        return result_text.strip() or "(Sem resposta textual do Gemini)"
    return getattr(response, 'text', '') or "(Sem resposta textual do Gemini)"


class GeminiOSFMCPServer:
    """Servidor MCP integrado com Gemini e funcionalidades OSF."""

    def __init__(self):
        self.gemini_model = None
        self.chat_session = None
        self.conversation_history = []
        self.current_search_results = None
        self.current_downloaded_files = None
        self.current_download_dir = None
        self.current_analysis = None
        self.setup_gemini()
    
    def setup_gemini(self):
        """Configura o modelo Gemini com ferramentas MCP."""
        if not HAS_GEMINI:
            print("❌ Google Gemini não disponível")
            return

        # Obter chave da API
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ GOOGLE_API_KEY não encontrada no arquivo .env")
            return

        try:
            genai.configure(api_key=api_key)

            # Definir ferramentas disponíveis para o Gemini
            tools = [
                {
                    "function_declarations": [
                        {
                            "name": "search_osf_mcp_tool",
                            "description": "Busca projetos científicos no OSF.io",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "description": "Termo de busca para projetos OSF"
                                    },
                                    "max_results": {
                                        "type": "string",
                                        "description": "Número máximo de resultados (padrão: '5')"
                                    }
                                },
                                "required": ["query"]
                            }
                        },
                        {
                            "name": "download_osf_mcp_tool",
                            "description": "Faz download de arquivos de um projeto OSF",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "project_url": {
                                        "type": "string",
                                        "description": "URL do projeto OSF para download"
                                    },
                                    "download_dir": {
                                        "type": "string",
                                        "description": "Diretório de destino (padrão: osf_downloads)"
                                    }
                                },
                                "required": ["project_url"]
                            }
                        },
                        {
                            "name": "analyze_data_mcp_tool",
                            "description": "Analisa dados de um arquivo CSV ou Excel",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "file_path": {
                                        "type": "string",
                                        "description": "Caminho para o arquivo de dados (opcional - usa arquivo baixado automaticamente se vazio)"
                                    }
                                },
                                "required": []
                            }
                        },
                        {
                            "name": "create_plot_mcp_tool",
                            "description": "Cria gráficos de dados científicos",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "file_path": {
                                        "type": "string",
                                        "description": "Caminho para o arquivo de dados (opcional - usa arquivo baixado automaticamente se vazio)"
                                    },
                                    "plot_type": {
                                        "type": "string",
                                        "description": "Tipo de gráfico: histogram, correlation, scatter",
                                        "enum": ["histogram", "correlation", "scatter"]
                                    }
                                },
                                "required": []
                            }
                        },
                        {
                            "name": "get_download_info_mcp_tool",
                            "description": "Obtém informações sobre o diretório de download e estrutura de arquivos",
                            "parameters": {
                                "type": "object",
                                "properties": {},
                                "required": []
                            }
                        }
                    ]
                }
            ]

            # Tentar diferentes modelos disponíveis com ferramentas
            try:
                print("🔄 Tentando configurar Gemini 1.5 Flash com ferramentas...")
                self.gemini_model = genai.GenerativeModel(
                    'gemini-1.5-flash',
                    tools=tools,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.7,
                        top_p=0.8,
                        top_k=40,
                        max_output_tokens=8192,
                    )
                )
                print("✅ Google Gemini configurado com ferramentas MCP (gemini-1.5-flash)")
            except Exception as e1:
                print(f"⚠️ Erro com gemini-1.5-flash: {e1}")
                try:
                    print("🔄 Tentando configurar Gemini 1.5 Pro com ferramentas...")
                    self.gemini_model = genai.GenerativeModel(
                        'gemini-1.5-pro',
                        tools=tools,
                        generation_config=genai.types.GenerationConfig(
                            temperature=0.7,
                            top_p=0.8,
                            top_k=40,
                            max_output_tokens=8192,
                        )
                    )
                    print("✅ Google Gemini configurado com ferramentas MCP (gemini-1.5-pro)")
                except Exception as e2:
                    print(f"⚠️ Erro com gemini-1.5-pro: {e2}")
                    # Fallback sem ferramentas se não funcionar
                    try:
                        print("🔄 Fallback: configurando Gemini sem ferramentas...")
                        self.gemini_model = genai.GenerativeModel(
                            'gemini-1.5-flash',
                            generation_config=genai.types.GenerationConfig(
                                temperature=0.7,
                                top_p=0.8,
                                top_k=40,
                                max_output_tokens=8192,
                            )
                        )
                        print("⚠️ Google Gemini configurado sem ferramentas (gemini-1.5-flash)")
                    except Exception as e3:
                        print(f"❌ Erro crítico ao configurar Gemini: {e3}")
                        self.gemini_model = None
        except Exception as e:
            print(f"❌ Erro ao configurar Gemini: {e}")
            self.gemini_model = None

    def start_chat_session(self):
        """Inicia uma nova sessão de chat com ferramentas MCP."""
        if not self.gemini_model:
            print("❌ Modelo Gemini não disponível")
            return False

        try:
            print("🔄 Iniciando nova sessão de chat com ferramentas...")

            # Configurar o chat com contexto de pesquisa científica e análise de dados
            system_prompt = """
            Você é um assistente especializado em pesquisa científica e análise de dados.
            Você pode:
            1. Ajudar a buscar projetos de pesquisa no OSF.io usando a função search_osf
            2. Fazer download de arquivos de pesquisa usando a função download_osf_files
            3. Analisar dados científicos baixados usando a função analyze_data
            4. Criar visualizações e gráficos de dados usando a função create_plot
            5. Interpretar resultados de análises estatísticas
            6. Sugerir metodologias de análise apropriadas
            7. Explicar conceitos de pesquisa científica

            IMPORTANTE: Você tem acesso às seguintes ferramentas MCP:
            - search_osf_mcp_tool(query, max_results): Busca projetos no OSF.io
            - download_osf_mcp_tool(project_url, download_dir): Faz download de arquivos de projetos
            - analyze_data_mcp_tool(file_path): Analisa dados de arquivos CSV/Excel
            - create_plot_mcp_tool(file_path, plot_type): Cria gráficos dos dados
            - get_download_info_mcp_tool(): Obtém informações sobre diretório de download e estrutura de arquivos

            Quando o usuário pedir para buscar, baixar ou analisar dados, USE ESSAS FERRAMENTAS MCP.
            Sempre seja didático, científico e forneça explicações detalhadas.
            Use emojis para tornar as explicações mais interessantes.
            Quando apropriado, sugira visualizações e análises adicionais.

            REGRAS IMPORTANTES PARA CHAMADAS DE FUNÇÃO:
            1. Sempre forneça argumentos válidos para as funções
            2. Use apenas os nomes de função MCP exatos: search_osf_mcp_tool, download_osf_mcp_tool, analyze_data_mcp_tool, create_plot_mcp_tool, get_download_info_mcp_tool
            3. Para analyze_data_mcp_tool e create_plot_mcp_tool, o file_path é opcional - se não fornecido, usará arquivos baixados automaticamente
            4. Para search_osf_mcp_tool, max_results deve ser uma string (ex: "5")
            5. Se não tiver certeza sobre um argumento, use valores padrão sensatos
            """

            # Iniciar chat com histórico vazio
            self.chat_session = self.gemini_model.start_chat(history=[])
            self.conversation_history = []

            # Enviar prompt inicial (sem salvar resposta)
            print("📤 Enviando prompt inicial...")
            response = self.chat_session.send_message(system_prompt)
            print("✅ Sessão de chat iniciada com ferramentas configuradas")

            return True

        except Exception as e:
            print(f"❌ Erro ao iniciar chat: {e}")
            import traceback
            traceback.print_exc()

            # Tentar fallback sem configurações avançadas
            try:
                print("🔄 Tentando fallback simples...")
                self.chat_session = self.gemini_model.start_chat(history=[])
                self.conversation_history = []
                print("⚠️ Sessão de chat iniciada em modo simplificado")
                return True
            except Exception as e2:
                print(f"❌ Erro no fallback: {e2}")
                return False

    def send_chat_message(self, message: str) -> str:
        """Envia mensagem para o chat e retorna resposta, mantendo o histórico da sessão e tratando function_call corretamente."""
        if not self.chat_session:
            if not self.start_chat_session():
                return "❌ Erro: Não foi possível iniciar sessão de chat com Gemini."

        try:
            print(f"🔄 Enviando mensagem para Gemini: {message[:100]}...")
            response = self.chat_session.send_message(message)
            print(f"📨 Resposta recebida do Gemini")

            # Tratar respostas com partes (function_call/text)
            result_text = ""
            function_results = []

            # Verificar se a resposta tem candidatos
            if not hasattr(response, 'candidates') or not response.candidates:
                print("⚠️ Resposta sem candidatos")
                return "❌ Resposta inválida do Gemini (sem candidatos)"

            candidate = response.candidates[0]
            if not hasattr(candidate, 'content') or not candidate.content:
                print("⚠️ Candidato sem conteúdo")
                return "❌ Resposta inválida do Gemini (sem conteúdo)"

            # Verificar se há partes no conteúdo
            if hasattr(candidate.content, 'parts') and candidate.content.parts:
                print(f"📋 Processando {len(candidate.content.parts)} partes da resposta")

                for i, part in enumerate(candidate.content.parts):
                    print(f"🔍 Processando parte {i+1}: {type(part)}")

                    # Se for texto, concatena
                    if hasattr(part, 'text') and part.text:
                        print(f"📝 Texto encontrado: {len(part.text)} caracteres")
                        result_text += part.text

                    # Se for function_call, executa a função correspondente
                    elif hasattr(part, 'function_call') and part.function_call:
                        print(f"🔧 Function call encontrado")
                        try:
                            func_call = part.function_call
                            func_name = getattr(func_call, 'name', None)
                            func_args = getattr(func_call, 'args', None)

                            print(f"🔧 Função: {func_name}")
                            print(f"🔧 Argumentos: {func_args}")

                            # Processar argumentos
                            if func_args is None:
                                func_args = {}
                            elif isinstance(func_args, str):
                                import json
                                try:
                                    func_args = json.loads(func_args)
                                except Exception as e:
                                    print(f"⚠️ Erro ao parsear argumentos JSON: {e}")
                                    func_args = {}
                            elif hasattr(func_args, '_pb'):
                                # Se for um objeto protobuf, converter para dict
                                try:
                                    func_args = dict(func_args)
                                except Exception as e:
                                    print(f"⚠️ Erro ao converter protobuf para dict: {e}")
                                    func_args = {}

                            if func_name:
                                print(f"🚀 Executando função: {func_name}")
                                function_result = self.execute_function(func_name, func_args or {})
                                function_results.append(function_result)
                                print(f"✅ Função executada: {len(function_result)} caracteres de resultado")
                            else:
                                print("⚠️ Nome da função não encontrado")

                        except Exception as e:
                            error_msg = f"❌ Erro ao processar function_call: {str(e)}"
                            print(error_msg)
                            function_results.append(error_msg)
                    else:
                        print(f"⚠️ Parte desconhecida: {type(part)}")

                # Combinar texto e resultados de função
                if function_results:
                    if result_text:
                        result_text += "\n\n" + "\n\n".join(function_results)
                    else:
                        result_text = "\n\n".join(function_results)

                if not result_text:
                    result_text = "❌ Nenhum conteúdo válido na resposta do Gemini"

            else:
                # Fallback para resposta simples
                print("📝 Usando fallback para resposta simples")
                result_text = getattr(response, 'text', '') or getattr(candidate.content, 'text', '') or "❌ Sem resposta textual do Gemini"

            # Salvar no histórico
            self.conversation_history.append({
                'user': message,
                'assistant': result_text,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })

            print(f"✅ Resposta processada: {len(result_text)} caracteres")
            return result_text

        except Exception as e:
            error_msg = f"❌ Erro na conversa: {str(e)}"
            print(f"❌ Erro detalhado: {e}")
            import traceback
            traceback.print_exc()
            return error_msg

    def execute_function(self, function_name: str, function_args: dict) -> str:
        """Executa uma função chamada pelo Gemini."""
        try:
            print(f"🔧 Executando função: {function_name}")
            print(f"🔧 Argumentos recebidos: {function_args}")
            print(f"🔧 Tipo dos argumentos: {type(function_args)}")

            # Garantir que function_args é um dict
            if not isinstance(function_args, dict):
                print(f"⚠️ Argumentos não são dict, convertendo: {function_args}")
                if hasattr(function_args, '__dict__'):
                    function_args = function_args.__dict__
                else:
                    function_args = {}

            # Suporte para nomes MCP padrão - usar sempre as funções MCP
            if function_name in ["search_osf_mcp_tool", "search_osf"]:
                query = function_args.get("query", "")
                max_results = function_args.get("max_results", 5)
                print(f"🔍 Executando busca OSF: query='{query}', max_results={max_results}")
                return search_osf_mcp_tool(query, str(max_results))

            elif function_name in ["download_osf_mcp_tool", "download_osf_files"]:
                project_url = function_args.get("project_url", "")
                download_dir = function_args.get("download_dir", "osf_downloads")
                print(f"📥 Executando download OSF: url='{project_url}', dir='{download_dir}'")
                return download_osf_mcp_tool(project_url, download_dir)

            elif function_name in ["analyze_data_mcp_tool", "analyze_data"]:
                file_path = function_args.get("file_path", "")
                print(f"📊 Executando análise de dados: file_path='{file_path}'")
                return analyze_data_mcp_tool(file_path)

            elif function_name in ["create_plot_mcp_tool", "create_plot"]:
                file_path = function_args.get("file_path", "")
                plot_type = function_args.get("plot_type", "histogram")
                print(f"📈 Executando criação de gráfico: file_path='{file_path}', plot_type='{plot_type}'")
                result = create_plot_mcp_tool(file_path, plot_type)
                if isinstance(result, str) and "Erro" in result:
                    return result
                else:
                    return f"✅ Gráfico criado com sucesso para {file_path} (tipo: {plot_type})"

            elif function_name in ["get_download_info_mcp_tool", "get_download_info"]:
                print(f"📁 Executando obtenção de informações de download")
                return get_download_info_mcp_tool()

            else:
                available_functions = [
                    "search_osf_mcp_tool",
                    "download_osf_mcp_tool",
                    "analyze_data_mcp_tool",
                    "create_plot_mcp_tool",
                    "get_download_info_mcp_tool"
                ]
                return f"❌ Função desconhecida: '{function_name}'\n\nFunções MCP disponíveis:\n" + "\n".join(f"• {func}" for func in available_functions)

        except Exception as e:
            error_msg = f"❌ Erro ao executar função '{function_name}': {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            return error_msg

    def get_conversation_history(self) -> str:
        """Retorna histórico da conversa formatado."""
        if not self.conversation_history:
            return "Nenhuma conversa ainda. Comece fazendo uma pergunta!"

        history_text = "## 📚 Histórico da Conversa\n\n"

        for i, entry in enumerate(self.conversation_history, 1):
            history_text += f"### 💬 Mensagem {i} ({entry['timestamp']})\n"
            history_text += f"**Você:** {entry['user']}\n\n"
            history_text += f"**Gemini:** {entry['assistant']}\n\n"
            history_text += "---\n\n"

        return history_text

    def clear_conversation(self):
        """Limpa a conversa atual e reinicia a sessão de chat com ferramentas."""
        self.conversation_history = []
        self.chat_session = None
        self.current_search_results = None
        self.current_downloaded_files = None
        self.current_download_dir = None
        self.current_analysis = None

        # Reiniciar sessão de chat para garantir que as ferramentas estejam disponíveis
        if self.gemini_model:
            print("🔄 Reiniciando sessão de chat com ferramentas...")
            self.start_chat_session()
    
    def extract_coefficients_with_gemini(self, text: str) -> Optional[Tuple[float, float, float]]:
        """Extrai coeficientes usando Gemini."""
        if not self.gemini_model:
            return None
        
        prompt = f"""
Extraia os coeficientes a, b, c da equação quadrática no seguinte texto:
"{text}"

A equação deve estar na forma ax² + bx + c = 0 ou f(x) = ax² + bx + c

Responda APENAS com três números separados por vírgula, na ordem a,b,c
Exemplo: 1,-5,6

Se não conseguir identificar uma equação quadrática válida, responda: ERRO

Texto: {text}
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            result = response.text.strip()
            
            if result == "ERRO":
                return None
            
            coeffs = result.split(',')
            if len(coeffs) == 3:
                a = float(coeffs[0].strip())
                b = float(coeffs[1].strip())
                c = float(coeffs[2].strip())
                return (a, b, c)
            else:
                return None
                
        except Exception as e:
            print(f"Erro ao extrair coeficientes com Gemini: {e}")
            return None
    
    def explain_with_gemini(self, equation_text: str, mcp_result: Dict) -> str:
        """Gera explicação didática usando Gemini."""
        if not self.gemini_model:
            return json.dumps(mcp_result, indent=2, ensure_ascii=False)
        
        prompt = f"""
Com base na seguinte solução de equação quadrática, forneça uma explicação didática e completa:

Equação original: {equation_text}
Resultado do cálculo:
{json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Forneça uma explicação que inclua:
1. A equação na forma padrão
2. Explicação do discriminante e seu significado
3. Interpretação das raízes encontradas
4. Conceitos matemáticos relevantes
5. Verificação das soluções (se possível)

Seja didático, educativo e use emojis para tornar mais interessante.
Explique cada passo de forma clara para estudantes.
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Erro na explicação: {str(e)}\n\nResultado bruto:\n{json.dumps(mcp_result, indent=2, ensure_ascii=False)}"

    def search_osf_with_gemini(self, query: str, max_results: int = 5) -> str:
        """Busca no OSF.io e analisa resultados com Gemini."""
        if not HAS_OSF_SCRAPER:
            return "❌ Funcionalidades OSF não disponíveis. Instale as dependências necessárias."

        try:
            # Corrigir tipo de max_results se vier como string
            if not isinstance(max_results, int):
                try:
                    max_results = int(max_results)
                except Exception:
                    max_results = 5

            print(f"🔍 Iniciando busca OSF para: {query}")

            # Fazer busca real no OSF
            try:
                print("🔍 Fazendo busca real no OSF...")
                results = search_osf(query, max_results=max_results*2, use_selenium=True)  # Buscar mais para filtrar
                if results and len(results) > 0:
                    print(f"✅ Busca real encontrou {len(results)} resultados")
                    # Verificar quais projetos têm arquivos
                    results_with_files = self.filter_projects_with_files(results)
                    if results_with_files:
                        self.current_search_results = results_with_files[:max_results]
                        print(f"✅ {len(self.current_search_results)} projetos com arquivos encontrados")
                        projetos_com_arquivos = self.current_search_results
                    else:
                        print("❌ Nenhum projeto com arquivos encontrado")
                        return f"❌ Nenhum projeto com arquivos encontrado para '{query}'. Os projetos encontrados não possuem arquivos disponíveis para download."
                else:
                    print("❌ Busca não retornou resultados")
                    return f"❌ Nenhum projeto encontrado para '{query}'. Tente usar termos mais específicos ou diferentes palavras-chave."

            except Exception as search_error:
                print(f"❌ Erro na busca: {search_error}")
                return f"❌ Erro ao buscar projetos no OSF.io: {str(search_error)}. Verifique sua conexão com a internet e tente novamente."

            if not projetos_com_arquivos:
                return f"❌ Nenhum projeto com arquivos encontrado para '{query}'. Tente outros termos de busca."

            # Formatar resultados para análise do Gemini
            results_text = f"Encontrados {len(projetos_com_arquivos)} projetos com arquivos para '{query}':\n\n"
            for i, result in enumerate(projetos_com_arquivos, 1):
                results_text += f"{i}. **{result.get('title', 'Sem título')}**\n"
                results_text += f"   - URL: {result.get('url', 'N/A')}\n"
                results_text += f"   - Tipo: {result.get('type', 'N/A')}\n"
                results_text += f"   - Autores: {result.get('authors', 'N/A')}\n"
                if result.get('description'):
                    results_text += f"   - Descrição: {result['description'][:100]}...\n"
                results_text += f"   - ✅ **Tem arquivos disponíveis**\n"
                if result.get('file_types'):
                    file_types = ', '.join(result['file_types'])
                    results_text += f"   - 📁 Tipos de arquivo: {file_types}\n"
                if result.get('relevance_score'):
                    results_text += f"   - 🎯 Relevância: {result['relevance_score']}/10\n"
                results_text += "\n"

            if not projetos_com_arquivos:
                return f"❌ Nenhum projeto com arquivos encontrado para '{query}'. Tente outros termos."

            # Analisar com Gemini se disponível
            if self.gemini_model:
                analysis_prompt = f"""
                Analise os seguintes resultados de busca do OSF.io para a consulta '{query}':

                {results_text}

                IMPORTANTE: Todos os projetos listados foram verificados e TÊM ARQUIVOS DISPONÍVEIS para download e análise.

                Por favor:
                1. Resuma os principais temas encontrados
                2. Identifique os projetos mais relevantes para a consulta
                3. Destaque os tipos de dados disponíveis em cada projeto
                4. Sugira quais projetos seriam melhores para diferentes tipos de análise
                5. Recomende próximos passos (download, análise, visualização)

                Seja específico e didático, enfatizando que estes projetos têm dados reais para trabalhar.
                """
                try:
                    response = self.gemini_model.generate_content(analysis_prompt)
                    analysis_text = extract_gemini_response_text(response)
                    return f"{results_text}\n## 🤖 Análise do Gemini:\n\n{analysis_text}"
                except Exception as e:
                    return f"{results_text}\n❌ Erro na análise do Gemini: {str(e)}"
            else:
                return results_text

        except Exception as e:
            return f"❌ Erro na busca OSF: {str(e)}"



    def filter_projects_with_files(self, projects):
        """Filtra projetos que têm arquivos disponíveis."""
        projects_with_files = []

        print(f"🔍 Verificando arquivos em {len(projects)} projetos...")

        for i, project in enumerate(projects, 1):
            project_url = project.get('url', '')
            project_title = project.get('title', 'Sem título')

            print(f"📁 Verificando projeto {i}/{len(projects)}: {project_title[:50]}...")

            try:
                # Tentar verificar se o projeto tem arquivos
                if self.check_project_has_files(project_url):
                    project['has_files'] = True
                    projects_with_files.append(project)
                    print(f"✅ Projeto tem arquivos: {project_title[:50]}")
                else:
                    print(f"❌ Projeto sem arquivos: {project_title[:50]}")

            except Exception as e:
                print(f"⚠️ Erro ao verificar projeto {project_title[:30]}: {e}")
                # Em caso de erro, assumir que tem arquivos para não perder o projeto
                project['has_files'] = 'unknown'
                projects_with_files.append(project)

        print(f"📊 {len(projects_with_files)} de {len(projects)} projetos têm arquivos")
        return projects_with_files

    def check_project_has_files(self, project_url):
        """Verifica se um projeto específico tem arquivos."""
        if not project_url:
            return False

        try:
            # Para URLs reais, tentar verificar usando o scraper
            if HAS_OSF_SCRAPER:
                try:
                    # Usar o scraper para verificar se há arquivos
                    from src.osf_scraper import check_project_files
                    return check_project_files(project_url)
                except Exception as e:
                    print(f"⚠️ Erro ao verificar arquivos com scraper: {e}")
                    # Fallback: tentar verificar pela URL de download
                    return self.check_files_by_download_url(project_url)

            # Se não tiver scraper, tentar verificar pela URL de download
            return self.check_files_by_download_url(project_url)

        except Exception as e:
            print(f"⚠️ Erro ao verificar arquivos em {project_url}: {e}")
            return False  # Em caso de erro, assumir que não tem arquivos

    def check_files_by_download_url(self, project_url):
        """Verifica se um projeto tem arquivos tentando acessar a URL de download."""
        import re
        import requests

        try:
            # Extrair código do projeto da URL
            project_code = None
            patterns = [
                r'osf\.io/([a-zA-Z0-9]+)/?',  # https://osf.io/j4bv6/
                r'osf\.io/([a-zA-Z0-9]+)$'    # https://osf.io/j4bv6
            ]

            for pattern in patterns:
                match = re.search(pattern, project_url)
                if match:
                    project_code = match.group(1)
                    break

            if not project_code:
                print(f"❌ Não foi possível extrair código do projeto da URL: {project_url}")
                return False

            # URL para verificar arquivos
            download_url = f"https://files.osf.io/v1/resources/{project_code}/providers/osfstorage/?zip="

            # Fazer requisição HEAD para verificar se há arquivos
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.head(download_url, timeout=10, headers=headers, allow_redirects=True)

            # Se status 200 e content-length > 0, tem arquivos
            if response.status_code == 200:
                content_length = int(response.headers.get('content-length', '0'))
                return content_length > 1000  # Pelo menos 1KB indica arquivos reais
            else:
                return False

        except Exception as e:
            print(f"⚠️ Erro ao verificar arquivos por URL: {e}")
            return False





    def download_real_osf_files(self, project_url: str, download_dir: str = "osf_downloads"):
        """Faz download real de arquivos OSF usando a URL correta do OSF."""
        import os
        import requests
        import re
        import zipfile
        import tempfile

        print(f"🔄 Tentando download real de: {project_url}")

        # Criar diretório se não existir
        os.makedirs(download_dir, exist_ok=True)

        # Extrair código do projeto da URL
        project_code = None
        patterns = [
            r'osf\.io/([a-zA-Z0-9]+)/?',  # https://osf.io/j4bv6/
            r'osf\.io/([a-zA-Z0-9]+)$'    # https://osf.io/j4bv6
        ]

        for pattern in patterns:
            match = re.search(pattern, project_url)
            if match:
                project_code = match.group(1)
                break

        if not project_code:
            print(f"❌ Não foi possível extrair código do projeto da URL: {project_url}")
            return []

        print(f"📋 Código do projeto extraído: {project_code}")

        # URL correta para download de arquivos OSF (formato ZIP)
        download_url = f"https://files.osf.io/v1/resources/{project_code}/providers/osfstorage/?zip="

        print(f"🌐 URL de download: {download_url}")

        downloaded_files = []

        try:
            # Configurar headers para parecer um navegador real
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            print(f"🌐 Fazendo requisição para: {download_url}")
            response = requests.get(download_url, timeout=30, stream=True, headers=headers, allow_redirects=True)

            print(f"📊 Status: {response.status_code}")
            print(f"📊 Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"📊 Content-Length: {response.headers.get('content-length', 'N/A')}")

            if response.status_code == 200:
                content_type = response.headers.get('content-type', '').lower()
                content_length = int(response.headers.get('content-length', '0'))

                # Verificar se é um arquivo ZIP válido
                if 'application/zip' in content_type or content_length > 1000:
                    print(f"✅ Arquivo ZIP encontrado ({content_length} bytes)")

                    # Salvar arquivo ZIP temporário
                    zip_path = os.path.join(download_dir, f"{project_code}_files.zip")

                    with open(zip_path, 'wb') as f:
                        total_size = 0
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                total_size += len(chunk)

                    print(f"📁 ZIP salvo: {zip_path} ({total_size} bytes)")

                    # Extrair arquivos do ZIP com descompactação recursiva
                    try:
                        extracted_files = self.extract_zip_recursively(zip_path, download_dir, project_code, project_url, download_url)
                        downloaded_files.extend(extracted_files)

                        # Remover arquivo ZIP após extração
                        os.remove(zip_path)
                        print(f"🗑️ Arquivo ZIP temporário removido")

                    except zipfile.BadZipFile:
                        print(f"❌ Arquivo não é um ZIP válido")
                        os.remove(zip_path)
                    except Exception as e:
                        print(f"❌ Erro ao extrair ZIP: {e}")
                        if os.path.exists(zip_path):
                            os.remove(zip_path)

                elif content_length == 0:
                    print(f"ℹ️ Projeto não tem arquivos disponíveis para download")
                    return []
                else:
                    print(f"⚠️ Resposta não é um arquivo ZIP válido (Content-Type: {content_type})")
                    return []

            elif response.status_code == 404:
                print(f"ℹ️ Projeto não encontrado ou não tem arquivos disponíveis")
                return []
            else:
                print(f"❌ Erro no download: Status {response.status_code}")
                return []

        except Exception as e:
            print(f"❌ Erro durante download: {e}")
            return []

        print(f"📊 Total de arquivos baixados: {len(downloaded_files)}")
        return downloaded_files

    def extract_zip_recursively(self, zip_path: str, extract_dir: str, project_code: str, project_url: str, download_url: str, depth: int = 0) -> list:
        """Extrai arquivos ZIP recursivamente, descompactando ZIPs aninhados."""
        import zipfile
        import os

        extracted_files = []
        max_depth = 5  # Limite de profundidade para evitar loops infinitos

        if depth > max_depth:
            print(f"⚠️ Profundidade máxima de extração atingida ({max_depth})")
            return extracted_files

        indent = "  " * depth
        print(f"{indent}📦 Extraindo ZIP (profundidade {depth}): {os.path.basename(zip_path)}")

        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                print(f"{indent}📋 Arquivos no ZIP: {len(file_list)}")

                # Primeiro, extrair todos os arquivos
                for file_name in file_list:
                    if not file_name.endswith('/'):  # Não é diretório
                        print(f"{indent}   📄 Extraindo: {file_name}")

                        try:
                            # Extrair arquivo
                            zip_ref.extract(file_name, extract_dir)

                            # Caminho do arquivo extraído
                            extracted_path = os.path.join(extract_dir, file_name)

                            if os.path.exists(extracted_path):
                                file_size = os.path.getsize(extracted_path)
                                file_info = {
                                    'name': os.path.basename(file_name),
                                    'size': f'{file_size // 1024} KB' if file_size > 1024 else f'{file_size} bytes',
                                    'local_path': extracted_path,
                                    'status': 'download_real_sucesso',
                                    'original_url': project_url,
                                    'download_url': download_url,
                                    'description': f'Arquivo extraído do projeto OSF {project_code} (profundidade {depth})',
                                    'extraction_depth': depth
                                }

                                # Verificar se é um arquivo ZIP
                                if file_name.lower().endswith('.zip'):
                                    print(f"{indent}   🔍 Arquivo ZIP aninhado encontrado: {file_name}")

                                    # Verificar se é um ZIP válido
                                    try:
                                        with zipfile.ZipFile(extracted_path, 'r') as test_zip:
                                            test_zip.testzip()

                                        print(f"{indent}   📦 Descompactando ZIP aninhado...")

                                        # Extrair recursivamente
                                        nested_files = self.extract_zip_recursively(
                                            extracted_path,
                                            extract_dir,
                                            project_code,
                                            project_url,
                                            download_url,
                                            depth + 1
                                        )

                                        # Adicionar arquivos aninhados
                                        extracted_files.extend(nested_files)

                                        # Remover o arquivo ZIP aninhado após extração
                                        os.remove(extracted_path)
                                        print(f"{indent}   🗑️ ZIP aninhado removido: {file_name}")

                                        # Não adicionar o ZIP à lista (foi removido)
                                        continue

                                    except zipfile.BadZipFile:
                                        print(f"{indent}   ⚠️ Arquivo ZIP corrompido ou inválido: {file_name}")
                                        file_info['description'] += ' (ZIP corrompido)'
                                    except Exception as e:
                                        print(f"{indent}   ⚠️ Erro ao processar ZIP aninhado {file_name}: {e}")
                                        file_info['description'] += f' (Erro: {str(e)})'

                                # Adicionar arquivo à lista
                                extracted_files.append(file_info)
                                print(f"{indent}   ✅ Extraído: {os.path.basename(file_name)} ({file_size} bytes)")

                        except Exception as e:
                            print(f"{indent}   ❌ Erro ao extrair {file_name}: {e}")

                print(f"{indent}✅ Extração concluída: {len(extracted_files)} arquivos")

        except zipfile.BadZipFile:
            print(f"{indent}❌ Arquivo não é um ZIP válido: {zip_path}")
        except Exception as e:
            print(f"{indent}❌ Erro ao extrair ZIP: {e}")

        return extracted_files

    def categorize_downloaded_files(self, files: list) -> str:
        """Categoriza arquivos baixados por tipo e sugere análises."""
        categories = {
            'dados_tabulares': [],
            'imagens': [],
            'documentos': [],
            'codigo': [],
            'outros': []
        }

        # Extensões por categoria
        tabular_exts = ['.csv', '.xlsx', '.xls', '.tsv', '.dat']
        image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.svg']
        doc_exts = ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf']
        code_exts = ['.py', '.r', '.m', '.sas', '.spss', '.sql', '.json', '.xml']

        for file_info in files:
            file_name = file_info.get('name', '').lower()
            file_path = file_info.get('local_path', '')
            file_size = file_info.get('size', 'N/A')
            depth = file_info.get('extraction_depth', 0)

            # Determinar categoria
            if any(file_name.endswith(ext) for ext in tabular_exts):
                categories['dados_tabulares'].append({
                    'name': file_info.get('name'),
                    'size': file_size,
                    'path': file_path,
                    'depth': depth
                })
            elif any(file_name.endswith(ext) for ext in image_exts):
                categories['imagens'].append({
                    'name': file_info.get('name'),
                    'size': file_size,
                    'path': file_path,
                    'depth': depth
                })
            elif any(file_name.endswith(ext) for ext in doc_exts):
                categories['documentos'].append({
                    'name': file_info.get('name'),
                    'size': file_size,
                    'path': file_path,
                    'depth': depth
                })
            elif any(file_name.endswith(ext) for ext in code_exts):
                categories['codigo'].append({
                    'name': file_info.get('name'),
                    'size': file_size,
                    'path': file_path,
                    'depth': depth
                })
            else:
                categories['outros'].append({
                    'name': file_info.get('name'),
                    'size': file_size,
                    'path': file_path,
                    'depth': depth
                })

        # Gerar texto de categorização
        categorization_text = ""

        if categories['dados_tabulares']:
            categorization_text += f"📊 **DADOS TABULARES** ({len(categories['dados_tabulares'])} arquivos):\n"
            for file in categories['dados_tabulares']:
                depth_indicator = "  " * file['depth'] + "└─ " if file['depth'] > 0 else "- "
                categorization_text += f"   {depth_indicator}{file['name']} ({file['size']})\n"
            categorization_text += "   → Análises sugeridas: Estatísticas descritivas, correlações, regressões, visualizações\n\n"

        if categories['imagens']:
            categorization_text += f"🖼️ **IMAGENS** ({len(categories['imagens'])} arquivos):\n"
            for file in categories['imagens']:
                depth_indicator = "  " * file['depth'] + "└─ " if file['depth'] > 0 else "- "
                categorization_text += f"   {depth_indicator}{file['name']} ({file['size']})\n"
            categorization_text += "   → Análises sugeridas: Análise de imagens, processamento visual, classificação\n\n"

        if categories['documentos']:
            categorization_text += f"📄 **DOCUMENTOS** ({len(categories['documentos'])} arquivos):\n"
            for file in categories['documentos']:
                depth_indicator = "  " * file['depth'] + "└─ " if file['depth'] > 0 else "- "
                categorization_text += f"   {depth_indicator}{file['name']} ({file['size']})\n"
            categorization_text += "   → Análises sugeridas: Análise de texto, extração de informações, revisão de metodologia\n\n"

        if categories['codigo']:
            categorization_text += f"💻 **CÓDIGO** ({len(categories['codigo'])} arquivos):\n"
            for file in categories['codigo']:
                depth_indicator = "  " * file['depth'] + "└─ " if file['depth'] > 0 else "- "
                categorization_text += f"   {depth_indicator}{file['name']} ({file['size']})\n"
            categorization_text += "   → Análises sugeridas: Revisão de código, reprodução de análises, validação de métodos\n\n"

        if categories['outros']:
            categorization_text += f"📁 **OUTROS ARQUIVOS** ({len(categories['outros'])} arquivos):\n"
            for file in categories['outros']:
                depth_indicator = "  " * file['depth'] + "└─ " if file['depth'] > 0 else "- "
                categorization_text += f"   {depth_indicator}{file['name']} ({file['size']})\n"
            categorization_text += "   → Análises sugeridas: Investigação manual do conteúdo\n\n"

        return categorization_text

    def get_download_directory_info(self) -> str:
        """Retorna informações sobre o diretório de download atual e sua estrutura."""
        if not self.current_download_dir or not os.path.exists(self.current_download_dir):
            return "❌ Nenhum diretório de download disponível."

        try:
            info_text = f"📁 **Diretório de download atual:** `{self.current_download_dir}`\n\n"

            # Listar todos os arquivos no diretório recursivamente
            all_files = []
            for root, dirs, files in os.walk(self.current_download_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.current_download_dir)
                    file_size = os.path.getsize(file_path)
                    all_files.append({
                        'name': file,
                        'relative_path': rel_path,
                        'full_path': file_path,
                        'size': file_size,
                        'size_str': f'{file_size // 1024} KB' if file_size > 1024 else f'{file_size} bytes'
                    })

            if all_files:
                info_text += f"📊 **Total de arquivos encontrados:** {len(all_files)}\n\n"
                info_text += "📋 **Estrutura de arquivos:**\n"

                for file_info in all_files:
                    depth = file_info['relative_path'].count(os.sep)
                    indent = "  " * depth
                    info_text += f"{indent}📄 {file_info['name']} ({file_info['size_str']})\n"
                    info_text += f"{indent}   Caminho: `{file_info['full_path']}`\n"
            else:
                info_text += "❌ Nenhum arquivo encontrado no diretório.\n"

            return info_text

        except Exception as e:
            return f"❌ Erro ao analisar diretório: {str(e)}"

    def download_osf_files_with_gemini(self, project_url: str, download_dir: str = "osf_downloads") -> str:
        """Faz download de arquivos OSF e analisa com Gemini."""
        if not HAS_OSF_SCRAPER:
            return "❌ Funcionalidades OSF não disponíveis. Instale as dependências necessárias."

        try:
            print(f"📥 Iniciando download de: {project_url}")

            # Tentar download real primeiro
            files = self.download_real_osf_files(project_url, download_dir)
            self.current_downloaded_files = files

            # Armazenar o diretório de download para uso posterior
            self.current_download_dir = os.path.abspath(download_dir)

            if not files:
                # Se não há arquivos, informar ao usuário
                return f"ℹ️ O projeto {project_url} não possui arquivos disponíveis para download.\n\nIsso pode acontecer quando:\n- O projeto não tem arquivos públicos\n- Os arquivos estão em repositórios privados\n- O projeto ainda não foi finalizado\n\nTente buscar outros projetos que tenham arquivos disponíveis."
            else:
                files_text = f"✅ Download concluído! {len(files)} arquivos baixados de {project_url}:\n\n"
                files_text += f"📁 **Diretório de download:** `{self.current_download_dir}`\n\n"

            # Formatar informações dos arquivos
            for i, file_info in enumerate(files, 1):
                files_text += f"{i}. **{file_info.get('name', 'Sem nome')}**\n"
                files_text += f"   - Tamanho: {file_info.get('size', 'N/A')}\n"
                files_text += f"   - Caminho local: {file_info.get('local_path', 'N/A')}\n"
                files_text += f"   - Status: {file_info.get('status', 'N/A')}\n"
                if file_info.get('description'):
                    files_text += f"   - Descrição: {file_info['description']}\n"
                files_text += "\n"

            # Analisar com Gemini se disponível
            if self.gemini_model:
                # Categorizar arquivos por tipo
                file_categories = self.categorize_downloaded_files(files)

                analysis_prompt = f"""
                Analise os seguintes arquivos extraídos do projeto OSF.io (incluindo descompactação recursiva de ZIPs):

                {files_text}

                ## Categorização dos Arquivos:
                {file_categories}

                Por favor:
                1. Identifique os tipos de dados disponíveis e sua relevância científica
                2. Sugira análises estatísticas apropriadas para cada tipo de arquivo
                3. Recomende visualizações específicas (histogramas, scatter plots, correlações, etc.)
                4. Proponha hipóteses de pesquisa baseadas nos tipos de dados encontrados
                5. Sugira a ordem ideal de análise dos arquivos
                6. Identifique possíveis relações entre diferentes arquivos
                7. Recomende próximos passos para análise e visualização

                IMPORTANTE: Todos os arquivos ZIP foram automaticamente descompactados recursivamente.
                Seja específico sobre métodos de análise de dados científicos.
                """

                try:
                    response = self.gemini_model.generate_content(analysis_prompt)
                    analysis_text = extract_gemini_response_text(response)
                    return f"{files_text}\n## 🤖 Análise do Gemini:\n\n{analysis_text}"
                except Exception as e:
                    return f"{files_text}\n❌ Erro na análise do Gemini: {str(e)}"
            else:
                return files_text

        except Exception as e:
            return f"❌ Erro no download OSF: {str(e)}"


# Instância global do servidor
gemini_server = GeminiOSFMCPServer()


def process_user_request(user_input: str) -> Tuple[str, str]:
    """
    Processa solicitação do usuário usando Gemini + MCP para OSF.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com resposta e dados JSON
    """
    if not user_input.strip():
        return "❌ Por favor, digite sua solicitação.", "{}"

    print(f"🔍 Processando entrada: {user_input}")

    # Analisar a intenção do usuário com Gemini
    if gemini_server.gemini_model:
        intent_prompt = f"""
        Analise a seguinte solicitação do usuário e determine a intenção:
        "{user_input}"

        Responda apenas com uma das seguintes opções:
        - SEARCH_OSF: se o usuário quer buscar projetos no OSF.io
        - DOWNLOAD_OSF: se o usuário quer fazer download de arquivos
        - ANALYZE_DATA: se o usuário quer analisar dados
        - CREATE_PLOT: se o usuário quer criar gráficos
        - CHAT: se é uma pergunta geral ou conversa

        Resposta:
        """

        try:
            intent_response = gemini_server.gemini_model.generate_content(intent_prompt)
            intent = intent_response.text.strip()
            print(f"🎯 Intenção detectada: {intent}")
        except:
            intent = "CHAT"
    else:
        # Fallback: detectar intenção por palavras-chave
        user_lower = user_input.lower()
        if any(word in user_lower for word in ['buscar', 'search', 'procurar', 'encontrar']):
            intent = "SEARCH_OSF"
        elif any(word in user_lower for word in ['download', 'baixar', 'arquivos']):
            intent = "DOWNLOAD_OSF"
        elif any(word in user_lower for word in ['analisar', 'análise', 'dados']):
            intent = "ANALYZE_DATA"
        elif any(word in user_lower for word in ['gráfico', 'plot', 'visualizar']):
            intent = "CREATE_PLOT"
        else:
            intent = "CHAT"

    # Processar baseado na intenção
    try:
        print(f"🎯 Processando intenção: {intent}")

        if intent == "SEARCH_OSF":
            print("🔍 Executando busca OSF...")
            # Extrair termo de busca
            if gemini_server.gemini_model:
                extract_prompt = f"""
                Extraia o termo de busca da seguinte solicitação:
                "{user_input}"

                Responda apenas com o termo de busca, sem explicações.
                """
                try:
                    extract_response = gemini_server.gemini_model.generate_content(extract_prompt)
                    search_term = extract_response.text.strip().strip('"\'')
                    print(f"🔍 Termo extraído pelo Gemini: {search_term}")
                except Exception as e:
                    print(f"⚠️ Erro na extração, usando input original: {e}")
                    search_term = user_input
            else:
                search_term = user_input

            print(f"🔍 Chamando search_osf_with_gemini com termo: {search_term}")
            result = gemini_server.search_osf_with_gemini(search_term, 5)
            print(f"✅ Resultado da busca: {len(result)} caracteres")
            return result, json.dumps({"action": "search", "term": search_term}, indent=2)

        elif intent == "DOWNLOAD_OSF":
            print("📥 Executando download OSF...")
            # Extrair URL do projeto
            import re
            url_pattern = r'https?://osf\.io/[a-zA-Z0-9]+/?'
            urls = re.findall(url_pattern, user_input)

            if urls:
                project_url = urls[0]
                print(f"📥 URL encontrada: {project_url}")
                result = gemini_server.download_osf_files_with_gemini(project_url)
                print(f"✅ Resultado do download: {len(result)} caracteres")
                return result, json.dumps({"action": "download", "url": project_url}, indent=2)
            else:
                print("❌ Nenhuma URL OSF encontrada na solicitação")
                # Verificar se o usuário se refere a um resultado anterior
                if any(word in user_input.lower() for word in ['primeiro', 'segundo', 'terceiro', 'resultado', 'projeto']):
                    if gemini_server.current_search_results:
                        # Usar o primeiro resultado da busca anterior
                        first_result = gemini_server.current_search_results[0]
                        project_url = first_result.get('url', '')
                        if project_url:
                            print(f"📥 Usando URL do primeiro resultado: {project_url}")
                            result = gemini_server.download_osf_files_with_gemini(project_url)
                            print(f"✅ Resultado do download: {len(result)} caracteres")
                            return result, json.dumps({"action": "download", "url": project_url}, indent=2)
                        else:
                            return "❌ O primeiro resultado não tem uma URL válida.", "{}"
                    else:
                        return "❌ Nenhuma busca anterior encontrada. Primeiro faça uma busca ou forneça uma URL do OSF.io.", "{}"
                else:
                    return "❌ Não consegui encontrar uma URL válida do OSF.io na sua solicitação. Forneça uma URL como https://osf.io/j4bv6/ ou faça uma busca primeiro.", "{}"

        elif intent == "ANALYZE_DATA":
            # Verificar se há arquivos baixados
            if gemini_server.current_downloaded_files:
                # Analisar o primeiro arquivo CSV/Excel encontrado
                for file_info in gemini_server.current_downloaded_files:
                    file_path = file_info.get('local_path', '')
                    if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                        result = analyze_data_mcp_tool(file_path)
                        return result, json.dumps({"action": "analyze", "file": file_path}, indent=2)

                return "❌ Nenhum arquivo de dados (CSV/Excel) encontrado nos downloads.", "{}"
            else:
                return "❌ Nenhum arquivo foi baixado ainda. Primeiro faça download de um projeto OSF.", "{}"

        elif intent == "CREATE_PLOT":
            # Verificar se há arquivos baixados
            if gemini_server.current_downloaded_files:
                # Criar gráfico do primeiro arquivo encontrado
                for file_info in gemini_server.current_downloaded_files:
                    file_path = file_info.get('local_path', '')
                    if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                        # Determinar tipo de gráfico
                        plot_type = "histogram"
                        if "correlação" in user_input.lower() or "correlation" in user_input.lower():
                            plot_type = "correlation"
                        elif "scatter" in user_input.lower() or "dispersão" in user_input.lower():
                            plot_type = "scatter"

                        result = create_plot_mcp_tool(file_path, plot_type)
                        return f"Gráfico criado para {os.path.basename(file_path)}", result

                return "❌ Nenhum arquivo de dados encontrado para criar gráfico.", "{}"
            else:
                return "❌ Nenhum arquivo foi baixado ainda. Primeiro faça download de um projeto OSF.", "{}"

        else:  # CHAT
            response = gemini_server.send_chat_message(user_input)
            return response, json.dumps({"action": "chat", "message": user_input}, indent=2)

    except Exception as e:
        print(f"❌ Erro no processamento: {e}")
        return f"❌ Erro ao processar solicitação: {str(e)}", "{}"


def search_osf_mcp_tool(query: str, max_results: str = "5") -> str:
    """Ferramenta MCP para buscar no OSF.io."""
    try:
        max_results_int = int(max_results)
        result = gemini_server.search_osf_with_gemini(query, max_results_int)
        return result
    except Exception as e:
        return f"❌ Erro na busca OSF: {str(e)}"


def download_osf_mcp_tool(project_url: str, download_dir: str = "osf_downloads") -> str:
    """Ferramenta MCP para fazer download de arquivos OSF."""
    try:
        result = gemini_server.download_osf_files_with_gemini(project_url, download_dir)
        return result
    except Exception as e:
        return f"❌ Erro no download OSF: {str(e)}"


def get_download_info_mcp_tool() -> str:
    """Ferramenta MCP para obter informações sobre o diretório de download."""
    try:
        result = gemini_server.get_download_directory_info()
        return result
    except Exception as e:
        return f"❌ Erro ao obter informações do diretório: {str(e)}"


def analyze_data_mcp_tool(file_path: str = "") -> str:
    """Ferramenta MCP para analisar dados de arquivo baixado."""
    try:
        # Se não foi fornecido um caminho específico, tentar encontrar arquivos automaticamente
        if not file_path:
            if gemini_server.current_downloaded_files:
                # Procurar o primeiro arquivo de dados disponível
                for file_info in gemini_server.current_downloaded_files:
                    potential_path = file_info.get('local_path', '')
                    if potential_path and potential_path.endswith(('.csv', '.xlsx', '.xls')):
                        file_path = potential_path
                        print(f"📊 Usando arquivo encontrado automaticamente: {file_path}")
                        break

                if not file_path:
                    return "❌ Nenhum arquivo de dados (CSV/Excel) encontrado nos downloads. Use get_download_info() para ver os arquivos disponíveis."
            else:
                return "❌ Nenhum arquivo foi baixado ainda. Primeiro faça download de um projeto OSF."

        if not os.path.exists(file_path):
            # Tentar caminho relativo ao diretório de download
            if gemini_server.current_download_dir:
                alternative_path = os.path.join(gemini_server.current_download_dir, file_path)
                if os.path.exists(alternative_path):
                    file_path = alternative_path
                else:
                    return f"❌ Arquivo não encontrado: {file_path}\n\nUse get_download_info() para ver os arquivos disponíveis."
            else:
                return f"❌ Arquivo não encontrado: {file_path}"

        # Determinar tipo de arquivo e analisar
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
            analysis = analyze_dataframe_with_gemini(df, file_path)
            return analysis
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
            analysis = analyze_dataframe_with_gemini(df, file_path)
            return analysis
        else:
            return f"❌ Tipo de arquivo não suportado: {file_path}\n\nTipos suportados: CSV, Excel (.xlsx, .xls)"

    except Exception as e:
        return f"❌ Erro na análise de dados: {str(e)}"


def create_plot_mcp_tool(file_path: str = "", plot_type: str = "histogram"):
    """Ferramenta MCP para criar gráficos de dados."""
    try:
        # Se não foi fornecido um caminho específico, tentar encontrar arquivos automaticamente
        if not file_path:
            if gemini_server.current_downloaded_files:
                # Procurar o primeiro arquivo de dados disponível
                for file_info in gemini_server.current_downloaded_files:
                    potential_path = file_info.get('local_path', '')
                    if potential_path and potential_path.endswith(('.csv', '.xlsx', '.xls')):
                        file_path = potential_path
                        print(f"🎨 Usando arquivo encontrado automaticamente: {file_path}")
                        break

                if not file_path:
                    print("❌ Nenhum arquivo de dados encontrado nos downloads")
                    return None
            else:
                print("❌ Nenhum arquivo foi baixado ainda")
                return None

        print(f"🎨 Tentando criar gráfico para: {file_path}")

        if not os.path.exists(file_path):
            # Tentar caminho relativo ao diretório de download
            if gemini_server.current_download_dir:
                alternative_path = os.path.join(gemini_server.current_download_dir, file_path)
                if os.path.exists(alternative_path):
                    file_path = alternative_path
                else:
                    print(f"❌ Arquivo não encontrado: {file_path}")
                    return None
            else:
                print(f"❌ Arquivo não encontrado: {file_path}")
                return None

        # Carregar dados
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            print(f"❌ Tipo de arquivo não suportado: {file_path}")
            return None

        print(f"📊 Dados carregados: {df.shape}")

        # Criar gráfico baseado no tipo
        fig = create_data_visualization(df, plot_type, file_path)

        if isinstance(fig, str) and "Erro" in fig:
            print(f"❌ Erro na visualização: {fig}")
            return None

        print(f"✅ Gráfico criado com sucesso")
        return fig

    except Exception as e:
        print(f"❌ Erro na criação do gráfico: {e}")
        return None


def analyze_dataframe_with_gemini(df: pd.DataFrame, file_path: str) -> str:
    """Analisa DataFrame com Gemini."""
    try:
        # Informações básicas do DataFrame
        info_text = f"""
## 📊 Análise do arquivo: {os.path.basename(file_path)}

### Informações Básicas:
- **Linhas:** {len(df)}
- **Colunas:** {len(df.columns)}
- **Colunas:** {', '.join(df.columns.tolist())}

### Tipos de Dados:
{df.dtypes.to_string()}

### Estatísticas Descritivas:
{df.describe().to_string()}

### Primeiras 5 linhas:
{df.head().to_string()}
"""

        # Analisar com Gemini se disponível
        if gemini_server.gemini_model:
            analysis_prompt = f"""
            Analise os seguintes dados científicos:

            {info_text}

            Por favor:
            1. Identifique padrões interessantes nos dados
            2. Sugira análises estatísticas apropriadas
            3. Recomende visualizações úteis
            4. Proponha hipóteses de pesquisa
            5. Identifique possíveis problemas nos dados (valores ausentes, outliers, etc.)

            Seja específico e científico na análise.
            """

            try:
                response = gemini_server.gemini_model.generate_content(analysis_prompt)
                return f"{info_text}\n## 🤖 Análise do Gemini:\n\n{response.text}"
            except Exception as e:
                return f"{info_text}\n❌ Erro na análise do Gemini: {str(e)}"
        else:
            return info_text

    except Exception as e:
        return f"❌ Erro na análise do DataFrame: {str(e)}"


def create_data_visualization(df: pd.DataFrame, plot_type: str, file_path: str):
    """Cria visualização de dados."""
    try:
        plt.style.use('default')

        if plot_type == "histogram":
            # Criar histogramas para colunas numéricas
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                return "❌ Nenhuma coluna numérica encontrada para histograma"

            n_cols = min(3, len(numeric_cols))
            n_rows = (len(numeric_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
            if n_rows == 1 and n_cols == 1:
                axes = [axes]
            elif n_rows == 1:
                axes = axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(numeric_cols):
                if i < len(axes):
                    df[col].hist(bins=20, ax=axes[i], alpha=0.7)
                    axes[i].set_title(f'Histograma: {col}')
                    axes[i].set_xlabel(col)
                    axes[i].set_ylabel('Frequência')

            # Remover subplots vazios
            for i in range(len(numeric_cols), len(axes)):
                fig.delaxes(axes[i])

            plt.tight_layout()
            return fig

        elif plot_type == "correlation":
            # Matriz de correlação
            numeric_df = df.select_dtypes(include=[np.number])
            if len(numeric_df.columns) < 2:
                return "❌ Pelo menos 2 colunas numéricas necessárias para correlação"

            fig, ax = plt.subplots(figsize=(10, 8))
            correlation_matrix = numeric_df.corr()
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, ax=ax)
            ax.set_title(f'Matriz de Correlação - {os.path.basename(file_path)}')
            plt.tight_layout()
            return fig

        elif plot_type == "scatter":
            # Scatter plot das duas primeiras colunas numéricas
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) < 2:
                return "❌ Pelo menos 2 colunas numéricas necessárias para scatter plot"

            fig, ax = plt.subplots(figsize=(10, 6))
            ax.scatter(df[numeric_cols[0]], df[numeric_cols[1]], alpha=0.6)
            ax.set_xlabel(numeric_cols[0])
            ax.set_ylabel(numeric_cols[1])
            ax.set_title(f'Scatter Plot: {numeric_cols[0]} vs {numeric_cols[1]}')
            plt.tight_layout()
            return fig

        else:
            return f"❌ Tipo de gráfico não suportado: {plot_type}"

    except Exception as e:
        return f"❌ Erro na criação da visualização: {str(e)}"


def process_with_visualization(user_input: str) -> Tuple[str, str, Optional[str]]:
    """
    Processa solicitação do usuário com visualização usando Gemini + MCP.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com resposta, dados JSON e gráfico (ou None)
    """
    print(f"🎨 Processando com visualização: {user_input}")

    # Primeiro processar normalmente
    response, json_result = process_user_request(user_input)

    print(f"📊 Resposta do processamento: {len(response)} caracteres")
    print(f"📈 JSON resultado: {json_result}")

    # Tentar criar visualização se apropriado
    try:
        # Verificar se há dados para visualizar
        if gemini_server.current_downloaded_files:
            print(f"📁 Arquivos disponíveis: {len(gemini_server.current_downloaded_files)}")
            for file_info in gemini_server.current_downloaded_files:
                file_path = file_info.get('local_path', '')
                print(f"📄 Verificando arquivo: {file_path}")
                if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                    print(f"🎨 Criando visualização para: {file_path}")
                    # Criar visualização automática
                    fig = create_plot_mcp_tool(file_path, "histogram")
                    if isinstance(fig, str) and "Erro" in fig:
                        print(f"❌ Erro na visualização: {fig}")
                        return response, json_result, None
                    return response, json_result, fig

        print("📊 Nenhum dado disponível para visualização")
        # Se não há dados, retornar sem gráfico
        return response, json_result, None

    except Exception as e:
        print(f"❌ Erro na visualização: {e}")
        return response, json_result, None


def interactive_chat(message: str, history: str) -> Tuple[str, str]:
    """
    Função para chat interativo com Gemini.

    Args:
        message: Nova mensagem do usuário
        history: Histórico da conversa

    Returns:
        Tuple com (resposta, histórico_atualizado)
    """
    if not message.strip():
        return "Por favor, digite uma mensagem.", history

    try:
        # Enviar mensagem para o Gemini
        response = gemini_server.send_chat_message(message)

        # Obter histórico atualizado
        updated_history = gemini_server.get_conversation_history()

        return response, updated_history

    except Exception as e:
        return f"❌ Erro no chat: {str(e)}", history


def clear_chat_history() -> Tuple[str, str]:
    """Limpa o histórico do chat."""
    try:
        gemini_server.clear_conversation()
        return "", "Conversa limpa! Comece uma nova discussão."
    except Exception as e:
        return "", f"Erro ao limpar conversa: {str(e)}"


def suggest_research_topics() -> str:
    """Sugere tópicos de pesquisa para discussão."""
    return """
## 💡 Sugestões de Comandos e Tópicos:

### 🔍 **Buscar Projetos OSF**
- "Buscar projetos sobre machine learning"
- "Procurar dados de psicologia experimental"
- "Encontrar estudos sobre neurociência"

### 📥 **Download de Dados**
- "Fazer download de https://osf.io/j4bv6/"
- "Baixar arquivos do projeto https://osf.io/2zfu4/"

### 📊 **Análise de Dados**
- "Analisar os dados baixados"
- "Criar estatísticas descritivas"
- "Verificar qualidade dos dados"

### 📈 **Visualizações**
- "Criar histograma dos dados"
- "Gerar matriz de correlação"
- "Fazer scatter plot das variáveis"

### 🤔 **Discussão Científica**
- "Como interpretar estes resultados?"
- "Que análises estatísticas são apropriadas?"
- "Como melhorar a qualidade dos dados?"

### 🧪 **Dicas de Busca**
- Use termos específicos em inglês para melhores resultados
- Combine palavras-chave relacionadas ao seu tema de pesquisa

**Digite qualquer comando ou pergunta para começar!** 🚀
    """


def create_gemini_osf_interface():
    """Cria interface Gradio integrada com Gemini e MCP para OSF."""
    with gr.Blocks(
        title="Gemini + MCP - Pesquisa Científica OSF",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .gemini-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
        }
        .mcp-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .chat-memory {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 10px;
            border-radius: 6px;
            margin: 5px 0;
            font-size: 0.9em;
        }
        """
    ) as demo:
        gr.HTML("""
        <div class=\"gemini-header\">
            <h1>🤖 Gemini + MCP - Pesquisa Científica OSF</h1>
            <p><strong>Inteligência Artificial + Análise de Dados Científicos</strong></p>
            <p>Busque, baixe e analise dados do OSF.io com IA!</p>
        </div>
        """)

        gr.Markdown("### 🎯 Chat Inteligente com Memória + Análise de Dados")
        gr.HTML("""
        <div class="chat-memory">
            <strong>💭 O Gemini lembra de toda nossa conversa!</strong>
            Use o chat para discussões contínuas sobre dados científicos.
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=2):
                main_input = gr.Textbox(
                    label="💬 Digite sua mensagem ou comando",
                    placeholder="Ex: Buscar machine learning, Analisar dados, ou qualquer pergunta científica...",
                    lines=3
                )
                with gr.Row():
                    send_btn = gr.Button("🚀 Enviar para Gemini", variant="primary", scale=3)
                    clear_all_btn = gr.Button("🗑️ Limpar Conversa", variant="secondary", scale=1)
                gr.Markdown("#### 🧩 Comandos Rápidos")
                with gr.Row():
                    ex1_btn = gr.Button("Buscar machine learning", size="sm")
                    ex2_btn = gr.Button("Buscar gait analysis", size="sm")
                with gr.Row():
                    ex3_btn = gr.Button("Analisar dados baixados", size="sm")
                    ex4_btn = gr.Button("Criar gráfico dos dados", size="sm")
                conversation_history = gr.Markdown(
                    value=suggest_research_topics(),
                    label="Conversa e Análises",
                    elem_id="conversation_area"
                )
            with gr.Column(scale=2):
                interactive_plot = gr.Plot(
                    label="Gráfico Interativo",
                    value=None
                )
                technical_data = gr.JSON(
                    label="Dados Técnicos",
                    show_label=True
                )
        current_gemini_response = gr.Markdown(
            label="Resposta Atual do Gemini",
            value="Digite uma equação ou pergunta acima para começar! 🤖"
        )
        status_display = gr.Markdown(
            value="**Status:** Pronto para análise ✅"
        )
        gr.HTML("""
        <div class=\"mcp-info\">
            <h3>📡 Configuração do Servidor</h3>
            <p><strong>URL MCP:</strong> <code>http://localhost:7861/gradio_api/mcp/sse</code></p>
            <p><strong>Status Gemini:</strong> """ + ("✅ Ativo" if gemini_server.gemini_model else "❌ Inativo") + """</p>
            <p><strong>Status OSF Scraper:</strong> """ + ("✅ Ativo" if HAS_OSF_SCRAPER else "❌ Inativo") + """</p>
            <p><strong>Ferramentas Disponíveis:</strong></p>
            <ul>
                <li><code>search_osf_mcp_tool</code> - Busca projetos no OSF.io</li>
                <li><code>download_osf_mcp_tool</code> - Faz download de arquivos OSF</li>
                <li><code>analyze_data_mcp_tool</code> - Analisa dados científicos</li>
                <li><code>create_plot_mcp_tool</code> - Cria visualizações de dados</li>
                <li><code>process_user_request</code> - Processa solicitações com Gemini</li>
                <li><code>interactive_chat</code> - Chat interativo com Gemini</li>
            </ul>
        </div>
        """)

        # Lógica de callbacks
        def on_send_click(user_input, history):
            response, updated_history = interactive_chat(user_input, history)
            return response, updated_history, gr.update(), gr.update(), "**Status:** Mensagem enviada ao Gemini ✅"

        def on_clear_all():
            clear_chat_history()
            return "Digite uma solicitação ou pergunta acima para começar! 🤖", suggest_research_topics(), None, None, "**Status:** Pronto para análise ✅"

        send_btn.click(
            on_send_click,
            inputs=[main_input, conversation_history],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        clear_all_btn.click(
            on_clear_all,
            inputs=[],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        # Comandos rápidos
        ex1_btn.click(lambda: "Buscar machine learning", None, main_input)
        ex2_btn.click(lambda: "Buscar gait analysis", None, main_input)
        ex3_btn.click(lambda: "Analisar dados baixados", None, main_input)
        ex4_btn.click(lambda: "Criar gráfico dos dados", None, main_input)
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando Servidor Gemini + MCP + OSF...")
    print("=" * 60)

    # Status das dependências
    print("📦 Status das Dependências:")
    print(f"   • python-dotenv: {'✅' if HAS_DOTENV else '❌'}")
    print(f"   • google-generativeai: {'✅' if HAS_GEMINI else '❌'}")
    print(f"   • quadratic_solver: {'✅' if HAS_QUADRATIC_SOLVER else '❌ (usando básico)'}")
    print(f"   • advanced_plotting: {'✅' if HAS_ADVANCED_PLOTTING else '❌'}")
    print(f"   • osf_scraper: {'✅' if HAS_OSF_SCRAPER else '❌'}")

    # Status do Gemini
    if gemini_server.gemini_model:
        print("   • Gemini Model: ✅ Configurado")
    else:
        print("   • Gemini Model: ❌ Não configurado")
        print("         💡 Verifique se GOOGLE_API_KEY está no arquivo .env")



    # Configurações do servidor
    host = os.getenv("MCP_SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("MCP_SERVER_PORT", "7861"))

    # Função para encontrar porta disponível
    def find_available_port(start_port=7861, max_attempts=10):
        import socket
        for i in range(max_attempts):
            test_port = start_port + i
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', test_port))
                    return test_port
            except OSError:
                continue
        return start_port  # Fallback para porta original

    # Encontrar porta disponível
    available_port = find_available_port(port)
    if available_port != port:
        print(f"⚠️ Porta {port} ocupada, usando porta {available_port}")
        port = available_port

    print(f"\n📡 Configurações do Servidor:")
    print(f"   • Host: {host}")
    print(f"   • Porta: {port}")
    print(f"   • URL: http://localhost:{port}")
    print(f"   • MCP Endpoint: http://localhost:{port}/gradio_api/mcp/sse")

    # Criar e lançar interface
    demo = create_gemini_osf_interface()

    print(f"\n🎯 Acesse a interface em: http://localhost:{port}")
    print("🤖 Digite comandos para buscar, baixar e analisar dados do OSF.io!")
    print("\n📋 Comandos disponíveis:")
    print("   • Buscar projetos: 'Buscar machine learning' ou 'Buscar gait analysis'")
    print("   • Download: 'Download [URL_DO_PROJETO_OSF]'")
    print("   • Analisar: 'Analisar dados baixados'")
    print("   • Gráficos: 'Criar gráfico dos dados'")

    try:
        demo.launch(
            server_name=host,
            server_port=port,
            mcp_server=True,
            share=False,
            show_error=True,
            show_api=True
        )
    except OSError as e:
        if "Cannot find empty port" in str(e):
            print(f"❌ Erro de porta: {e}")
            print("💡 Tente usar uma porta diferente:")
            print("   GRADIO_SERVER_PORT=7862 python gemini_mcp_server.py")
        else:
            raise


if __name__ == "__main__":
    main()
