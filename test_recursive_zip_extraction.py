#!/usr/bin/env python3
"""
Teste para verificar a descompactação recursiva de arquivos ZIP.
"""

import sys
import os
import zipfile
import tempfile
import shutil

# Adicionar diretório atual ao path
sys.path.insert(0, '.')

def create_test_nested_zip():
    """Cria um arquivo ZIP com ZIPs aninhados para teste."""
    print("🔧 Criando estrutura de teste com ZIPs aninhados...")
    
    # Criar diretório temporário
    test_dir = tempfile.mkdtemp(prefix="test_zip_")
    print(f"📁 Diretório de teste: {test_dir}")
    
    # Criar arquivos de dados de teste
    data1_path = os.path.join(test_dir, "data1.csv")
    with open(data1_path, 'w') as f:
        f.write("id,value,category\n1,10.5,A\n2,20.3,B\n3,15.7,A\n")
    
    data2_path = os.path.join(test_dir, "data2.txt")
    with open(data2_path, 'w') as f:
        f.write("Este é um arquivo de texto de exemplo.\nContém informações importantes para análise.")
    
    # Criar primeiro ZIP interno (level2.zip)
    level2_zip = os.path.join(test_dir, "level2.zip")
    with zipfile.ZipFile(level2_zip, 'w') as zf:
        zf.write(data1_path, "data1.csv")
        zf.write(data2_path, "data2.txt")
    
    # Criar arquivo adicional para o nível 1
    data3_path = os.path.join(test_dir, "analysis.py")
    with open(data3_path, 'w') as f:
        f.write("# Script de análise\nimport pandas as pd\nprint('Análise de dados')")
    
    # Criar segundo ZIP interno (level1.zip) que contém level2.zip
    level1_zip = os.path.join(test_dir, "level1.zip")
    with zipfile.ZipFile(level1_zip, 'w') as zf:
        zf.write(level2_zip, "level2.zip")
        zf.write(data3_path, "analysis.py")
    
    # Criar arquivo para o nível principal
    readme_path = os.path.join(test_dir, "README.md")
    with open(readme_path, 'w') as f:
        f.write("# Projeto de Teste\nEste é um projeto com ZIPs aninhados para testar a extração recursiva.")
    
    # Criar ZIP principal que contém tudo
    main_zip = os.path.join(test_dir, "main_project.zip")
    with zipfile.ZipFile(main_zip, 'w') as zf:
        zf.write(level1_zip, "level1.zip")
        zf.write(readme_path, "README.md")
    
    print(f"✅ ZIP de teste criado: {main_zip}")
    print("📦 Estrutura:")
    print("   main_project.zip")
    print("   ├── README.md")
    print("   └── level1.zip")
    print("       ├── analysis.py")
    print("       └── level2.zip")
    print("           ├── data1.csv")
    print("           └── data2.txt")
    
    return main_zip, test_dir

def test_recursive_extraction():
    """Testa a função de extração recursiva."""
    print("🧪 TESTE DE EXTRAÇÃO RECURSIVA DE ZIP")
    print("=" * 60)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Criar ZIP de teste
        test_zip, test_dir = create_test_nested_zip()
        
        # Criar diretório de extração
        extract_dir = os.path.join(test_dir, "extracted")
        os.makedirs(extract_dir, exist_ok=True)
        
        print(f"\n🔍 Testando extração recursiva...")
        print(f"   ZIP de origem: {test_zip}")
        print(f"   Diretório de extração: {extract_dir}")
        
        # Testar função de extração recursiva
        extracted_files = gemini_server.extract_zip_recursively(
            test_zip, 
            extract_dir, 
            "test_project", 
            "https://osf.io/test/", 
            "test_url",
            0
        )
        
        print(f"\n📊 RESULTADOS:")
        print(f"   Total de arquivos extraídos: {len(extracted_files)}")
        
        # Verificar arquivos extraídos
        expected_files = ["README.md", "analysis.py", "data1.csv", "data2.txt"]
        found_files = []
        
        for file_info in extracted_files:
            name = file_info.get('name')
            path = file_info.get('local_path')
            size = file_info.get('size')
            depth = file_info.get('extraction_depth', 0)
            
            print(f"   📄 {name} ({size}) - Profundidade: {depth}")
            if os.path.exists(path):
                print(f"      ✅ Arquivo existe: {path}")
                found_files.append(name)
            else:
                print(f"      ❌ Arquivo não encontrado: {path}")
        
        # Verificar se todos os arquivos esperados foram encontrados
        missing_files = set(expected_files) - set(found_files)
        extra_files = set(found_files) - set(expected_files)
        
        print(f"\n🎯 VERIFICAÇÃO:")
        print(f"   Arquivos esperados: {len(expected_files)}")
        print(f"   Arquivos encontrados: {len(found_files)}")
        
        if not missing_files and not extra_files:
            print("   ✅ Todos os arquivos foram extraídos corretamente!")
            success = True
        else:
            if missing_files:
                print(f"   ❌ Arquivos faltando: {missing_files}")
            if extra_files:
                print(f"   ⚠️ Arquivos extras: {extra_files}")
            success = len(found_files) >= len(expected_files) * 0.75  # 75% de sucesso
        
        # Verificar se não há ZIPs restantes
        remaining_zips = [f for f in found_files if f.endswith('.zip')]
        if remaining_zips:
            print(f"   ⚠️ ZIPs não extraídos: {remaining_zips}")
        else:
            print("   ✅ Todos os ZIPs foram extraídos recursivamente!")
        
        # Limpeza
        shutil.rmtree(test_dir)
        print(f"\n🧹 Diretório de teste removido: {test_dir}")
        
        return success
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_download_with_recursive_extraction():
    """Testa download real com extração recursiva."""
    print("\n🌐 TESTE DE DOWNLOAD COM EXTRAÇÃO RECURSIVA")
    print("=" * 60)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testar com projeto real
        test_url = "https://osf.io/j4bv6/"
        download_dir = "test_recursive_download"
        
        print(f"🔍 Testando download de: {test_url}")
        print(f"📁 Diretório de download: {download_dir}")
        
        # Fazer download
        result = gemini_server.download_osf_files_with_gemini(test_url, download_dir)
        
        print(f"\n📊 Resultado do download:")
        print(f"   Tamanho da resposta: {len(result)} caracteres")
        
        # Verificar arquivos baixados
        if gemini_server.current_downloaded_files:
            print(f"   Arquivos baixados: {len(gemini_server.current_downloaded_files)}")
            
            for file_info in gemini_server.current_downloaded_files:
                name = file_info.get('name')
                size = file_info.get('size')
                depth = file_info.get('extraction_depth', 0)
                status = file_info.get('status')
                
                print(f"   📄 {name} ({size}) - Profundidade: {depth} - Status: {status}")
            
            return True
        else:
            print("   ℹ️ Nenhum arquivo foi baixado (normal se o projeto não tem arquivos)")
            return True
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTE DE EXTRAÇÃO RECURSIVA DE ZIP")
    print("=" * 70)
    
    tests = [
        ("Extração recursiva", test_recursive_extraction),
        ("Download com extração", test_download_with_recursive_extraction)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Erro crítico em {test_name}: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*70}")
    print("📊 RESUMO DOS TESTES")
    print("="*70)
    
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"{test_name:25} {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"\n🎯 Resultado: {passed}/{total} testes passaram")
    
    if passed >= 1:
        print("🎉 EXTRAÇÃO RECURSIVA FUNCIONANDO!")
        print("\n💡 Funcionalidades implementadas:")
        print("   ✅ Extração recursiva de ZIPs aninhados")
        print("   ✅ Categorização automática de arquivos")
        print("   ✅ Análise inteligente com Gemini")
        print("   ✅ Sugestões de visualização")
        print("\n🚀 Agora você pode:")
        print("   1. Baixar projetos com ZIPs complexos")
        print("   2. Ver todos os arquivos extraídos automaticamente")
        print("   3. Receber sugestões de análise por tipo de arquivo")
        print("   4. Criar gráficos dos dados extraídos")
    else:
        print("⚠️ Alguns problemas na extração recursiva. Verifique os erros acima.")
