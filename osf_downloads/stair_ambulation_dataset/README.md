# Stair Ambulation Dataset
## Overview
As stairs are an essential part of our everyday lives and are frequently encountered in urban environments, stair ambulation sequences should be included in mobility analysis.
Therefore, this dataset was recorded in January 2021 to enable the development and evaluation of algorithms for human gait analysis using wearable inertial sensors for real-world applications, including level-walking gait as well as stair ascending and stair descending.

If you use this dataset please cite our related article:

- Roth N., Küderle A., Prossel D., Gassner H., Eskofier BM., Kluge F. An Inertial Sensor-Based Gait Analysis Pipeline for the Assessment of Real-World Stair Ambulation Parameters. Sensors. 2021; 21(19):6559. DOI: [10.3390/s21196559](https://doi.org/10.3390/s21196559)

## Working with the Data

For easy and convenient use of the dataset we provide a python module to interface the dataset:

```python
pip install mad-datasets
```

For more and detailed information how to work with the package please refer to our github page: [https://github.com/mad-lab-fau/mad-datasets](https://github.com/mad-lab-fau/mad-datasets)

If you want to load the raw sensor data binaries (stand alone) for some reason you can use the following package:

```python
pip install nilspodlib
```

For more and detailed information on this package refer to our github page: [https://github.com/mad-lab-fau/NilsPodLib](https://github.com/mad-lab-fau/NilsPodLib)

## What Data are Missing

In our original publication we included data of 20 healthy participants as well as 13 PD patients.
In the public version of the dataset only the data of the 20 healthy participants is included.
PD patient data is not available due to data protection.

Furthermore, our original dataset contains video recordings for each participant, however, due to privacy reasons we only included one anonymized example video recording.

## Known Issues

- For subject_04 the pressure insole of the left foot failed (due to a loose connector) for the last 2 tasks. Therefore, the pressure data from sample 169900 until the end are invalid.
- For subject_13 the study protocol and video for part_1 start at sample 71760! (Flying part was repeated for first staircase, because the camera battery went dead after the first run-through)

## Data Structure
```
stair_ambulation_dataset/                               # dataset parent 
├─ calibrations/                                        # sensor calibration files by sensor
│  ├─ 6f13/                                             # contains calibration for sensor ID 6F13
│  ├─ 7fe5/                                             # contains calibration for sensor ID 7FE5
│  ├─ 48b4/                                             # contains calibration for sensor ID 48B4
│  ├─ fsr_calibrations/                                 # contains calibrations for individual FSR sensors
├─ documentation/                                       # pictures and additional documentation
│  ├─ sensor_setup/                                     # pictures of sensor setup and coordinate system definitions
│  ├─ recording_environment/                            # pictures of recording environment / different stair setups
│  ├─ video/                                            # example video of one recording with labels of the individual tasks
├─ healthy/                                             # data per subject
│  ├─ subject_01/                                   
│  ├─ subject_02/
│  ├─ ...
│  ├─ subject_20/                                       
│  │  ├─ part_1/                                        # data of part_1 (recording was split into 2 sessions)
│  │  │  ├─ test_list.json                              # annotations of individual tasks (start and stop sample)
│  │  │  ├─ imu/                                        # raw sensor data
│  │  │  │  ├─ NilsPodX-6F13_YYYYMMDD_HHMMSS.bin        # sensor data (per sensor unit) in binary format 
│  │  │  │  ├─ NilsPodX-7FE5_YYYYMMDD_HHMMSS.bin        # (see our data loading library to load, convert and calibrate the data)
│  │  │  │  ├─ NilsPodX-48B4_YYYYMMDD_HHMMSS.bin
│  │  │  ├─ manual_annotations_z_level.csv              # manual annotations of stride borders, stride class and step height
│  │  │  ├─ pressure_events.csv                         # reference gait events (extracted from pressure insole data)
│  │  ├─ part_2/                                        # part_2 (has the same structure as part_1)
│  │  │  ├─ test_list.json
│  │  │  ├─ imu/
│  │  │  │  ├─ NilsPodX-6F13_YYYYMMDD_HHMMSS.bin
│  │  │  │  ├─ NilsPodX-7FE5_YYYYMMDD_HHMMSS.bin
│  │  │  │  ├─ NilsPodX-48B4_YYYYMMDD_HHMMSS.bin
│  │  │  ├─ manual_annotations_z_level.csv
│  │  │  ├─ pressure_events.csv 
│  │  ├─ metadata.json                                  # metadata of participant (demographics + assignment of the sensor ids)
```

## Stair Geometries

The dataset includes three very different geometries, including different step dimensions, landings, and turns.
Step dimensions are listed below, additional pictures of the stairs are available in the documentation folder or in the example video.

![stair_configuration_public_dataset.png](documentation/recording_environment/stair_configuration_public_dataset.png)

- staircase:
    - step height: 17.5 cm 
    - step length: 26.5 cm
    - step inclination: 33.4 deg
    - total number of steps: 48 (3x16)
- stair_long:
    - step height: 14.5 cm 
    - step length: 35.0 cm
    - step inclination: 22.5 deg
    - total number of steps: 29  
- stair_flat:
    - step height: 13.0 cm 
    - step length: 97.0 cm
    - step inclination: 7.6 deg
    - total number of steps: 9 
- ramp/slope:
    - total length: 35 m 
    - total height: 1.2 m
    - inclination: 1.9 deg

![google_maps_overview_of_recording_site.png](documentation/recording_environment/google_maps_overview_of_recording_site.png)


## Tasks

Naming convention of the individual tasks: "\<stair\>\_\<direction\>\_\<speed\>"

For each of the three stairs ("staircase", "stair_long", "stair_flat") 7 individual tasks exist. Namely, "flying_normal", "up_normal", "down_normal", "up_fast", "down_fast", "up_slow", "down_slow". 

Direction:
- Flying: This task refers to pass the stairs up and down in one continuous sequence without stopping.
  Before and after the stair sections participants performed multiple strides of level walking.
- Up: Walking up the stairs starting and ending with some level walking strides
- Down: Walking down the stairs starting and ending with some level walking strides

Speed:
- normal: participants were asked to walk in their self-selected preferred speed as they would do in their daily life
- fast: participant were asked to walk in self-selected fast speed but don't "run"
- slow: participant were asked to walk in self-selected slow speed

To mark the start and end of each task, participants were asked to perform three quick jumps between each task.

Each participant had to perform a total 26 of predefined tasks.

1. staircase_flying_normal
2. staircase_up_normal
3. staircase_down_normal
4. staircase_up_fast
5. staircase_down_fast
6. staircase_up_slow
7. staircase_down_slow
8. slope_ascending_normal
9. slope_descending_normal
10. stair_flat_down_normal
11. stair_flat_up_normal
12. stair_flat_down_fast
13. stair_flat_up_fast
14. stair_flat_down_slow
15. stair_flat_up_slow
16. stair_long_flying_normal
17. stair_long_down_normal
18. stair_long_up_normal
19. stair_long_down_fast
20. stair_long_up_fast
21. stair_long_down_slow
22. stair_long_up_slow
23. stair_long_down_single_step
24. stair_long_up_single_step
25. stair_long_down_double_step
26. stair_long_up_double_step

Tasks 23 to 26 were recorded to additionally simulate different stair ambulation strategies, namely taking every single stair step (which might be the case for high frailty persons) and taking only every second step (skipping steps) high dynamic stair ambulation.
Tasks 23 to 26 were only recorded on the "stair_long" stairs.

## Sensor Setup

Participants wore three wearable sensors (Portabiles GmbH, Erlangen, Germany):
- Left Foot: IMU (3D Acc + 3D Gyro) + Barometer + FSR pressure insole
- Right Foot: IMU (3D Acc + 3D Gyro) + Barometer + FSR pressure insole
- Lower Back: IMU (3D Acc + 3D Gyro) + Barometer

![sensor_coordinate_system_definitions.png](documentation/sensor_setup/sensor_coordinate_system_definitions.png)

(The right coordinate system is the one you will get after loading the data with the `mad-datasets` library.)

<p float="left">
  <img src="documentation/sensor_setup/sensor_setup_1.JPG" height="220" />
  <img src="documentation/sensor_setup/sensor_setup_2.JPG" height="220" />
  <img src="documentation/sensor_setup/pressure_sensor_locations_insole.jpg" height="220" />  
</p>

### Sensor Units
The IMU sensor units (Portabiles GmbH, Erlangen, Germany) featured a 3D accelerometer (range ±16g) a 3D gyroscope (range ±2000dps) and a barometric pressure sensor. The foot-worn sensor units were extended by custom FSR-based pressure insoles (including a non-inverting amplifier for signal-conditioning per pressure sensor) and connected to the 16-bit ADC of the sensor.

- IMU: Bosch [BMI160](https://www.bosch-sensortec.com/media/boschsensortec/downloads/datasheets/bst-bmi160-ds000.pdf) (Bosch Sensortec GmbH, Reutlingen, Germany)
- Barometer: [BMP280](https://www.bosch-sensortec.com/media/boschsensortec/downloads/datasheets/bst-bmp280-ds001.pdf) (Bosch Sensortec GmbH, Reutlingen, Germany)
- FSR: [RP-S40-ST](http://m.eleparts.co.kr/data/_gextends/good-pdf/201904/good-pdf-7495470-1.pdf) (40mm x 40mm)

All sensors were synchronized in real-time on a sample to sample level using 2.4Ghz proprietary radio protocol.
Sensor data were recorded with a sampling rate of 204.8Hz.

## Annotations

For each dataset manual annotations of stride borders (start, end in samples), stride type (class name) and height covered per stride/foot (in centimeters) are available (`manual_annotations_z_level.csv`). The height information can be, e.g., used to additionally distinguish single and double-step stair strides.
Annotations were performed based on IMU and video data (see video recording in the documentation folder).
In our original dataset video recordings of every participant are available, however, due to privacy reasons we only provide the example video in the public version of the dataset.

Additionally, gait event references are available (`pressure_events.csv`).
This includes reference to initial contact (IC), terminal contact (TC) and midstance (MS) per stride.
The reference labels are derived from the pressure sensor data together with the manual stride border annotations.

Gait event reference can be recalculated from a corresponding [script](https://github.com/mad-lab-fau/mad-datasets/tree/main/mad_datasets/stair_ambulation_healthy_2021/scripts) within the mad-dataset package

## Participants

Metadata to each participant is included in the "metadata.json" (see dataset structure)

Demographics overview of the 20 healthy participants

| Parameter     |  mean ± std | min | max |
|---------------|-------------|-----|-----|
| Gender [f/m]  | 10/10       |  -  |  -  |
| Age [years]   | 27.1 ± 11.3 | 19  | 53  |  
| Height [cm]   | 173.4 ± 7.1 | 160 | 185 |
| Weight [kg]   | 68.1 ± 8.6  | 55  | 83  |   

## References
This dataset was originally recorded as part of the following publication in MDPI Sensors 2021 (https://doi.org/10.3390/s21196559):

Roth N., Küderle A., Prossel D., Gassner H., Eskofier BM., Kluge F. An Inertial Sensor-Based Gait Analysis Pipeline for the Assessment of Real-World Stair Ambulation Parameters. Sensors. 2021; 21(19):6559. DOI: [10.3390/s21196559](https://doi.org/10.3390/s21196559)

The developed pipeline and stair ambulation parameters as digital mobility outcomes (DMOs) were also transferred to a first clinical application for fall risk prediction in Parkinson's Diseases:

Roth N., Ullrich U., Küderle A., Gladow T., Marxreiter F., Gassner H., Kluge F., Klucken J., and  Eskofier BM., "Real-World Stair Ambulation Characteristics Differ Between Prospective Fallers and Non-Fallers in Parkinson’s Disease," in IEEE Journal of Biomedical and Health Informatics, 2022, DOI: [10.1109/JBHI.2022.3186766](https://doi.org/10.1109/JBHI.2022.3186766).

